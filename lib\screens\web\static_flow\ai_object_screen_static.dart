import 'package:flutter/material.dart';
import 'package:nsl/screens/web/static_flow/extract_details_middle_static.dart';

void main() {
  runApp(const MaterialApp(home: AiObjectScreenStatic()));
}

class AiObjectScreenStatic extends StatelessWidget {
  const AiObjectScreenStatic({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('3 Column Layout')),
      body: Row(
        children: [
          // Column 1

          Expanded(
            child: Container(
              color: Colors.red[200], // background color

              child: const Center(
                child: Text(
                  'Column 1',
                  style: TextStyle(fontSize: 18, color: Colors.white),
                ),
              ),
            ),
          ),

          // Column 2

          Expanded(
            child: Container(
              color: Colors.green[200], // background color

              child: const ExtractDetailsMiddleStatic(),
            ),
          ),

          // Column 3

          Expanded(
            child: Container(
              color: Colors.blue[200], // background color

              child: const Center(
                child: Text(
                  'Column 3',
                  style: TextStyle(fontSize: 18, color: Colors.white),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
